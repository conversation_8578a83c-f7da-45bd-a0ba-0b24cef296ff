"""
ZoneServer IDA Pro Decompiler Script
Advanced Python script for decompiling ZoneServer binary in IDA Pro

This script runs inside IDA Pro and provides:
- Automatic function analysis and decompilation
- Class hierarchy reconstruction
- Game system identification
- Cross-reference analysis
- Export to multiple formats
- Comprehensive reporting

Usage:
1. Load ZoneServer binary in IDA Pro
2. Wait for auto-analysis to complete
3. Run this script: Alt+F7 or File -> Script file
4. Check output in the specified directory

Author: AI Assistant
Version: 2.0 (IDA Pro)
"""

import idaapi
import idautils
import idc
import ida_funcs
import ida_hexrays
import ida_name
import ida_bytes
import ida_ua
import ida_xref
import os
import re
import json
import time
from pathlib import Path

class IDAFunction:
    """Represents a function analyzed in IDA Pro"""
    def __init__(self, ea, name):
        self.ea = ea
        self.name = name
        self.end_ea = idc.find_func_end(ea)
        self.size = self.end_ea - ea if self.end_ea != idaapi.BADADDR else 0
        self.pseudocode = ""
        self.assembly = ""
        self.calls = []
        self.called_by = []
        self.class_name = ""
        self.is_constructor = False
        self.is_destructor = False
        self.xrefs_to = []
        self.xrefs_from = []
        
    def analyze(self):
        """Perform detailed analysis of the function"""
        # Get pseudocode if Hex-Rays is available
        try:
            cfunc = ida_hexrays.decompile(self.ea)
            if cfunc:
                self.pseudocode = str(cfunc)
        except:
            self.pseudocode = "// Decompilation failed"
        
        # Get assembly code
        self.assembly = self._get_assembly()
        
        # Analyze cross-references
        self._analyze_xrefs()
        
        # Detect class information
        self._detect_class_info()
    
    def _get_assembly(self):
        """Extract assembly code for the function"""
        asm_lines = []
        ea = self.ea
        while ea < self.end_ea and ea != idaapi.BADADDR:
            disasm = idc.generate_disasm_line(ea, 0)
            asm_lines.append("0x{:08X}: {}".format(ea, disasm))
            ea = idc.next_head(ea)
        return "\n".join(asm_lines)
    
    def _analyze_xrefs(self):
        """Analyze cross-references to and from this function"""
        # References TO this function
        for xref in idautils.XrefsTo(self.ea):
            caller_name = idc.get_func_name(xref.frm)
            if caller_name:
                self.called_by.append(caller_name)
        
        # References FROM this function
        for xref in idautils.XrefsFrom(self.ea):
            callee_name = idc.get_func_name(xref.to)
            if callee_name:
                self.calls.append(callee_name)
    
    def _detect_class_info(self):
        """Detect if this is a class method"""
        if "::" in self.name:
            parts = self.name.split("::")
            if len(parts) >= 2:
                self.class_name = parts[0]
                method_name = parts[1]
                
                # Check for constructor/destructor
                if method_name == self.class_name:
                    self.is_constructor = True
                elif method_name.startswith("~"):
                    self.is_destructor = True

class ZoneServerIDADecompiler:
    """IDA Pro decompiler for ZoneServer binary analysis"""
    
    def __init__(self, output_dir="zoneserver_analysis"):
        self.output_dir = Path(output_dir)
        self.functions = {}
        self.classes = {}
        self.game_systems = {}
        self.statistics = {}
        
        # Game system patterns for classification
        self.system_patterns = {
            'Authentication': ['CSAuth', 'Auth', 'Login', 'Verify'],
            'Networking': ['Net', 'Socket', 'Packet', 'Send', 'Recv'],
            'Player Management': ['Player', 'User', 'Character', 'Party'],
            'Game Objects': ['GameObject', 'Monster', 'NPC', 'Item'],
            'Map System': ['Map', 'Zone', 'Level', 'World'],
            'Guild System': ['Guild', 'Clan'],
            'Database': ['DB', 'SQL', 'Query', 'Record'],
            'Security': ['Crypt', 'Hash', 'Encrypt', 'Security'],
            'UI System': ['UI', 'Dialog', 'Window', 'View', 'Frame'],
            'Combat System': ['Attack', 'Damage', 'Skill', 'Battle']
        }
        
        self._setup_output_directory()
        
    def _setup_output_directory(self):
        """Create output directory structure"""
        self.output_dir.mkdir(exist_ok=True)
        (self.output_dir / "functions").mkdir(exist_ok=True)
        (self.output_dir / "classes").mkdir(exist_ok=True)
        (self.output_dir / "analysis").mkdir(exist_ok=True)
        (self.output_dir / "reports").mkdir(exist_ok=True)
        
    def log(self, message):
        """Simple logging function for IDA Pro"""
        print("[ZoneServer Decompiler] {}".format(message))
        
    def analyze_all_functions(self):
        """Analyze all functions in the binary"""
        self.log("Starting function analysis...")
        
        function_count = 0
        for func_ea in idautils.Functions():
            func_name = idc.get_func_name(func_ea)
            if not func_name:
                continue
                
            self.log("Analyzing function: {}".format(func_name))

            # Create function object and analyze
            ida_func = IDAFunction(func_ea, func_name)
            ida_func.analyze()

            self.functions[func_name] = ida_func
            function_count += 1

            # Progress indicator
            if function_count % 100 == 0:
                self.log("Processed {} functions...".format(function_count))

        self.log("Completed analysis of {} functions".format(function_count))
        
    def classify_game_systems(self):
        """Classify functions into game systems"""
        self.log("Classifying game systems...")
        
        for system_name, keywords in self.system_patterns.items():
            self.game_systems[system_name] = []
            
            for func_name, func in self.functions.items():
                for keyword in keywords:
                    if keyword.lower() in func_name.lower():
                        self.game_systems[system_name].append(func_name)
                        break
        
        # Remove empty systems
        self.game_systems = {k: v for k, v in self.game_systems.items() if v}
        
    def identify_classes(self):
        """Identify and group class methods"""
        self.log("Identifying classes...")
        
        for func_name, func in self.functions.items():
            if func.class_name:
                if func.class_name not in self.classes:
                    self.classes[func.class_name] = {
                        'methods': [],
                        'constructors': [],
                        'destructors': [],
                        'vtable_methods': []
                    }
                
                self.classes[func.class_name]['methods'].append(func_name)
                
                if func.is_constructor:
                    self.classes[func.class_name]['constructors'].append(func_name)
                elif func.is_destructor:
                    self.classes[func.class_name]['destructors'].append(func_name)
                
                # Check for vtable methods
                if 'vtbl' in func_name.lower() or 'vftable' in func_name.lower():
                    self.classes[func.class_name]['vtable_methods'].append(func_name)

        self.log("Identified {} classes".format(len(self.classes)))
        
    def generate_statistics(self):
        """Generate analysis statistics"""
        self.statistics = {
            'total_functions': len(self.functions),
            'total_classes': len(self.classes),
            'game_systems': len(self.game_systems),
            'largest_functions': [],
            'most_called_functions': [],
            'class_method_counts': {}
        }
        
        # Find largest functions
        sorted_by_size = sorted(self.functions.items(), 
                               key=lambda x: x[1].size, reverse=True)
        self.statistics['largest_functions'] = [
            (name, func.size) for name, func in sorted_by_size[:10]
        ]
        
        # Find most called functions
        sorted_by_calls = sorted(self.functions.items(), 
                                key=lambda x: len(x[1].called_by), reverse=True)
        self.statistics['most_called_functions'] = [
            (name, len(func.called_by)) for name, func in sorted_by_calls[:10]
        ]
        
        # Class method counts
        for class_name, class_data in self.classes.items():
            self.statistics['class_method_counts'][class_name] = len(class_data['methods'])

    def export_function_details(self):
        """Export detailed function analysis"""
        self.log("Exporting function details...")

        functions_dir = self.output_dir / "functions"

        for func_name, func in self.functions.items():
            # Create safe filename
            safe_name = re.sub(r'[^\w\-_\.]', '_', func_name)
            if len(safe_name) > 200:
                safe_name = safe_name[:200] + "_truncated"

            func_file = functions_dir / f"{safe_name}.md"

            try:
                with open(func_file, 'w') as f:
                    f.write("# Function: {}\n\n".format(func.name))
                    f.write("**Address:** 0x{:08X}\n".format(func.ea))
                    f.write("**Size:** {} bytes\n".format(func.size))
                    f.write("**End Address:** 0x{:08X}\n\n".format(func.end_ea))

                    if func.class_name:
                        f.write("**Class:** {}\n".format(func.class_name))
                        if func.is_constructor:
                            f.write("**Type:** Constructor\n")
                        elif func.is_destructor:
                            f.write("**Type:** Destructor\n")
                        f.write("\n")

                    if func.called_by:
                        f.write("## Called By\n\n")
                        for caller in func.called_by[:20]:  # Limit to first 20
                            f.write("- {}\n".format(caller))
                        if len(func.called_by) > 20:
                            f.write("- ... and {} more\n".format(len(func.called_by) - 20))
                        f.write("\n")

                    if func.calls:
                        f.write("## Calls\n\n")
                        for callee in func.calls[:20]:  # Limit to first 20
                            f.write("- {}\n".format(callee))
                        if len(func.calls) > 20:
                            f.write("- ... and {} more\n".format(len(func.calls) - 20))
                        f.write("\n")

                    if func.pseudocode:
                        f.write("## Pseudocode\n\n")
                        f.write("```c\n")
                        f.write(func.pseudocode)
                        f.write("\n```\n\n")

                    if func.assembly:
                        f.write("## Assembly\n\n")
                        f.write("```asm\n")
                        f.write(func.assembly)
                        f.write("\n```\n")

            except Exception as e:
                self.log("Error writing function {}: {}".format(func_name, e))

    def export_class_analysis(self):
        """Export class analysis"""
        self.log("Exporting class analysis...")

        classes_dir = self.output_dir / "classes"

        for class_name, class_data in self.classes.items():
            safe_name = re.sub(r'[^\w\-_\.]', '_', class_name)
            class_file = classes_dir / f"{safe_name}.md"

            try:
                with open(class_file, 'w', encoding='utf-8') as f:
                    f.write(f"# Class: {class_name}\n\n")
                    f.write(f"**Total Methods:** {len(class_data['methods'])}\n")
                    f.write(f"**Constructors:** {len(class_data['constructors'])}\n")
                    f.write(f"**Destructors:** {len(class_data['destructors'])}\n")
                    f.write(f"**VTable Methods:** {len(class_data['vtable_methods'])}\n\n")

                    if class_data['constructors']:
                        f.write("## Constructors\n\n")
                        for ctor in class_data['constructors']:
                            f.write(f"- {ctor}\n")
                        f.write("\n")

                    if class_data['destructors']:
                        f.write("## Destructors\n\n")
                        for dtor in class_data['destructors']:
                            f.write(f"- {dtor}\n")
                        f.write("\n")

                    if class_data['vtable_methods']:
                        f.write("## Virtual Table Methods\n\n")
                        for vtable_method in class_data['vtable_methods']:
                            f.write(f"- {vtable_method}\n")
                        f.write("\n")

                    f.write("## All Methods\n\n")
                    for method in sorted(class_data['methods']):
                        f.write(f"- {method}\n")

            except Exception as e:
                self.log(f"Error writing class {class_name}: {e}")

    def export_game_systems_analysis(self):
        """Export game systems analysis"""
        self.log("Exporting game systems analysis...")

        analysis_dir = self.output_dir / "analysis"
        systems_file = analysis_dir / "game_systems.md"

        try:
            with open(systems_file, 'w', encoding='utf-8') as f:
                f.write("# ZoneServer Game Systems Analysis\n\n")
                f.write("This report categorizes functions by game system.\n\n")

                for system_name, functions in self.game_systems.items():
                    if functions:
                        f.write(f"## {system_name}\n\n")
                        f.write(f"**Function Count:** {len(functions)}\n\n")

                        f.write("**Functions:**\n")
                        for func_name in sorted(functions)[:50]:  # Limit to first 50
                            f.write(f"- {func_name}\n")

                        if len(functions) > 50:
                            f.write(f"- ... and {len(functions) - 50} more functions\n")
                        f.write("\n")

        except Exception as e:
            self.log(f"Error writing game systems analysis: {e}")

    def export_statistics_report(self):
        """Export comprehensive statistics"""
        self.log("Exporting statistics report...")

        reports_dir = self.output_dir / "reports"
        stats_file = reports_dir / "statistics.md"

        try:
            with open(stats_file, 'w', encoding='utf-8') as f:
                f.write("# ZoneServer Analysis Statistics\n\n")

                f.write("## Overview\n\n")
                f.write(f"- **Total Functions:** {self.statistics['total_functions']}\n")
                f.write(f"- **Total Classes:** {self.statistics['total_classes']}\n")
                f.write(f"- **Game Systems:** {self.statistics['game_systems']}\n\n")

                f.write("## Largest Functions\n\n")
                f.write("| Function Name | Size (bytes) |\n")
                f.write("|---------------|-------------|\n")
                for name, size in self.statistics['largest_functions']:
                    f.write(f"| {name} | {size} |\n")
                f.write("\n")

                f.write("## Most Called Functions\n\n")
                f.write("| Function Name | Call Count |\n")
                f.write("|---------------|------------|\n")
                for name, count in self.statistics['most_called_functions']:
                    f.write(f"| {name} | {count} |\n")
                f.write("\n")

                f.write("## Classes by Method Count\n\n")
                f.write("| Class Name | Method Count |\n")
                f.write("|------------|-------------|\n")
                sorted_classes = sorted(self.statistics['class_method_counts'].items(),
                                      key=lambda x: x[1], reverse=True)
                for class_name, method_count in sorted_classes[:20]:
                    f.write(f"| {class_name} | {method_count} |\n")
                f.write("\n")

        except Exception as e:
            self.log(f"Error writing statistics report: {e}")

    def export_summary_report(self):
        """Export main summary report"""
        self.log("Exporting summary report...")

        summary_file = self.output_dir / "README.md"

        try:
            with open(summary_file, 'w', encoding='utf-8') as f:
                f.write("# ZoneServer IDA Pro Analysis Results\n\n")
                f.write("This directory contains the complete analysis of the ZoneServer binary.\n\n")

                f.write("## Analysis Summary\n\n")
                f.write(f"- **Binary:** {idc.get_input_file_path()}\n")
                f.write(f"- **Analysis Date:** {time.strftime('%Y-%m-%d %H:%M:%S')}\n")
                f.write(f"- **Total Functions:** {self.statistics['total_functions']}\n")
                f.write(f"- **Total Classes:** {self.statistics['total_classes']}\n")
                f.write(f"- **Game Systems Identified:** {self.statistics['game_systems']}\n\n")

                f.write("## Directory Structure\n\n")
                f.write("```\n")
                f.write("zoneserver_analysis/\n")
                f.write("├── functions/          # Individual function analysis\n")
                f.write("├── classes/            # Class analysis and methods\n")
                f.write("├── analysis/           # Game systems categorization\n")
                f.write("├── reports/            # Statistical reports\n")
                f.write("└── README.md           # This file\n")
                f.write("```\n\n")

                f.write("## Key Findings\n\n")

                # Top game systems
                f.write("### Major Game Systems\n\n")
                sorted_systems = sorted(self.game_systems.items(),
                                      key=lambda x: len(x[1]), reverse=True)
                for system_name, functions in sorted_systems[:5]:
                    f.write(f"- **{system_name}:** {len(functions)} functions\n")
                f.write("\n")

                # Top classes
                f.write("### Largest Classes\n\n")
                sorted_classes = sorted(self.statistics['class_method_counts'].items(),
                                      key=lambda x: x[1], reverse=True)
                for class_name, method_count in sorted_classes[:5]:
                    f.write(f"- **{class_name}:** {method_count} methods\n")
                f.write("\n")

                f.write("## Usage\n\n")
                f.write("1. Start with this README for an overview\n")
                f.write("2. Check `reports/statistics.md` for detailed statistics\n")
                f.write("3. Browse `analysis/game_systems.md` for system categorization\n")
                f.write("4. Explore individual functions in `functions/`\n")
                f.write("5. Study class hierarchies in `classes/`\n\n")

        except Exception as e:
            self.log(f"Error writing summary report: {e}")

    def export_json_data(self):
        """Export all data to JSON format"""
        self.log("Exporting JSON data...")

        json_data = {
            'metadata': {
                'binary_name': idc.get_input_file_path(),
                'analysis_time': time.strftime('%Y-%m-%d %H:%M:%S'),
                'ida_version': idaapi.get_kernel_version()
            },
            'statistics': self.statistics,
            'game_systems': self.game_systems,
            'functions': {
                name: {
                    'address': f"0x{func.ea:08X}",
                    'size': func.size,
                    'class_name': func.class_name,
                    'is_constructor': func.is_constructor,
                    'is_destructor': func.is_destructor,
                    'calls_count': len(func.calls),
                    'called_by_count': len(func.called_by)
                } for name, func in self.functions.items()
            },
            'classes': self.classes
        }

        json_file = self.output_dir / "zoneserver_analysis.json"
        try:
            with open(json_file, 'w', encoding='utf-8') as f:
                json.dump(json_data, f, indent=2)
            self.log(f"JSON data exported to {json_file}")
        except Exception as e:
            self.log(f"Error writing JSON data: {e}")

    def run_full_analysis(self):
        """Run complete analysis of the ZoneServer binary"""
        self.log("=" * 60)
        self.log("ZoneServer IDA Pro Decompiler Starting...")
        self.log("=" * 60)

        start_time = time.time()

        try:
            # Step 1: Analyze all functions
            self.analyze_all_functions()

            # Step 2: Classify into game systems
            self.classify_game_systems()

            # Step 3: Identify classes
            self.identify_classes()

            # Step 4: Generate statistics
            self.generate_statistics()

            # Step 5: Export all results
            self.export_function_details()
            self.export_class_analysis()
            self.export_game_systems_analysis()
            self.export_statistics_report()
            self.export_json_data()
            self.export_summary_report()

            end_time = time.time()
            duration = end_time - start_time

            self.log("=" * 60)
            self.log("Analysis Complete!")
            self.log(f"Duration: {duration:.2f} seconds")
            self.log(f"Results saved to: {self.output_dir}")
            self.log("=" * 60)

            # Show summary
            print(f"\n=== ZoneServer Analysis Summary ===")
            print(f"Functions analyzed: {self.statistics['total_functions']}")
            print(f"Classes identified: {self.statistics['total_classes']}")
            print(f"Game systems: {self.statistics['game_systems']}")
            print(f"Output directory: {self.output_dir}")
            print(f"Analysis time: {duration:.2f} seconds")

        except Exception as e:
            self.log(f"Error during analysis: {e}")
            import traceback
            traceback.print_exc()

def main():
    """Main function to run the ZoneServer decompiler"""
    print("ZoneServer IDA Pro Decompiler")
    print("=" * 40)

    # Check if we're running in IDA Pro
    try:
        binary_name = idc.get_input_file_path()
        print(f"Analyzing binary: {binary_name}")
    except:
        print("ERROR: This script must be run inside IDA Pro!")
        return

    # Check if Hex-Rays decompiler is available
    if not ida_hexrays.init_hexrays_plugin():
        print("WARNING: Hex-Rays decompiler not available. Pseudocode will not be generated.")
    else:
        print("Hex-Rays decompiler detected - pseudocode will be included.")

    # Create output directory name based on binary
    binary_base = os.path.splitext(os.path.basename(binary_name))[0]
    output_dir = f"{binary_base}_analysis"

    # Create and run decompiler
    decompiler = ZoneServerIDADecompiler(output_dir)
    decompiler.run_full_analysis()

# Run the analysis when script is executed
if __name__ == "__main__":
    main()
else:
    # When loaded as a script in IDA Pro
    main()
