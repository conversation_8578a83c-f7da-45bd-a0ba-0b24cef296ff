import idaapi
import idautils
import idc
import ida_funcs
import ida_hexrays
import os

def extract_all_functions():
    """Extract all functions with pseudocode and assembly"""
    
    # Create output directory
    output_dir = "extracted_functions"
    if not os.path.exists(output_dir):
        os.makedirs(output_dir)
    
    # Files for different outputs
    pseudocode_file = os.path.join(output_dir, "pseudocode.cpp")
    assembly_file = os.path.join(output_dir, "assembly.asm")
    function_list_file = os.path.join(output_dir, "function_list.txt")
    
    with open(pseudocode_file, 'w') as pc_f, \
         open(assembly_file, 'w') as asm_f, \
         open(function_list_file, 'w') as list_f:
        
        print("Starting function extraction...")
        
        # Iterate through all functions
        for func_ea in idautils.Functions():
            func_name = idc.get_func_name(func_ea)
            func_end = idc.find_func_end(func_ea)
            
            if not func_name:
                continue
                
            print(f"Processing: {func_name}")
            
            # Write function info to list
            list_f.write(f"Function: {func_name}\n")
            list_f.write(f"Address: 0x{func_ea:X} - 0x{func_end:X}\n")
            list_f.write(f"Size: {func_end - func_ea} bytes\n\n")
            
            # Extract pseudocode using Hex-Rays decompiler
            try:
                cfunc = ida_hexrays.decompile(func_ea)
                if cfunc:
                    pc_f.write(f"// Function: {func_name} at 0x{func_ea:X}\n")
                    pc_f.write(f"{str(cfunc)}\n\n")
                    pc_f.write("="*80 + "\n\n")
            except Exception as e:
                pc_f.write(f"// Failed to decompile {func_name}: {str(e)}\n\n")
            
            # Extract assembly code
            asm_f.write(f"; Function: {func_name} at 0x{func_ea:X}\n")
            asm_f.write(f"{func_name}:\n")
            
            # Get assembly instructions
            ea = func_ea
            while ea < func_end:
                disasm = idc.generate_disasm_line(ea, 0)
                asm_f.write(f"    {disasm}\n")
                ea = idc.next_head(ea)
            
            asm_f.write("\n" + "="*80 + "\n\n")
    
    print(f"Extraction complete! Files saved in {output_dir}/")

def extract_specific_functions(function_names):
    """Extract specific functions by name"""
    
    output_dir = "specific_functions"
    if not os.path.exists(output_dir):
        os.makedirs(output_dir)
    
    for func_name in function_names:
        func_ea = idc.get_name_ea_simple(func_name)
        if func_ea == idaapi.BADADDR:
            print(f"Function {func_name} not found")
            continue
            
        # Create individual file for each function
        filename = os.path.join(output_dir, f"{func_name}.cpp")
        with open(filename, 'w') as f:
            try:
                cfunc = ida_hexrays.decompile(func_ea)
                if cfunc:
                    f.write(f"// Function: {func_name}\n")
                    f.write(f"// Address: 0x{func_ea:X}\n\n")
                    f.write(str(cfunc))
                    print(f"Extracted: {func_name}")
            except Exception as e:
                f.write(f"// Failed to decompile: {str(e)}")

def extract_class_methods(class_prefix):
    """Extract all methods of a specific class"""
    
    output_dir = f"{class_prefix}_methods"
    if not os.path.exists(output_dir):
        os.makedirs(output_dir)
    
    class_file = os.path.join(output_dir, f"{class_prefix}_class.cpp")
    
    with open(class_file, 'w') as f:
        f.write(f"// Class: {class_prefix} methods\n\n")
        
        for func_ea in idautils.Functions():
            func_name = idc.get_func_name(func_ea)
            
            if func_name and class_prefix in func_name:
                print(f"Found class method: {func_name}")
                
                try:
                    cfunc = ida_hexrays.decompile(func_ea)
                    if cfunc:
                        f.write(f"// Method: {func_name}\n")
                        f.write(f"{str(cfunc)}\n\n")
                        f.write("="*60 + "\n\n")
                except Exception as e:
                    f.write(f"// Failed to decompile {func_name}: {str(e)}\n\n")

# Main execution
if __name__ == "__main__":
    print("IDA Pro Function Extractor")
    print("1. Extract all functions")
    print("2. Extract specific functions")
    print("3. Extract class methods")
    
    choice = input("Enter choice (1-3): ")
    
    if choice == "1":
        extract_all_functions()
    
    elif choice == "2":
        # Example: Extract specific game-related functions
        important_functions = [
            "CPlayer::Login",
            "CPlayer::Logout", 
            "CNetWorking::SendPacket",
            "CUserDB::LoadPlayer",
            "CCSAuth3::Init",
            "CGameObject::Update"
        ]
        extract_specific_functions(important_functions)
    
    elif choice == "3":
        class_name = input("Enter class prefix (e.g., CPlayer): ")
        extract_class_methods(class_name)
    
    print("Extraction completed!")