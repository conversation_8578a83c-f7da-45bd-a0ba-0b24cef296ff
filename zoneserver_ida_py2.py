"""
ZoneServer IDA Pro Decompiler Script (Python 2.7 Compatible)
For IDA Pro 6.8 and older versions using Python 2.7

Usage:
1. Load ZoneServer binary in IDA Pro
2. Wait for auto-analysis to complete
3. Run this script: Alt+F7 or File -> Script file
4. Check output in the specified directory

Author: AI Assistant
Version: 2.0 (Python 2.7)
"""

import idaapi
import idautils
import idc
import ida_funcs
import ida_hexrays
import ida_name
import ida_bytes
import ida_ua
import ida_xref
import os
import re
import json
import time

class IDAFunction:
    """Represents a function analyzed in IDA Pro"""
    def __init__(self, ea, name):
        self.ea = ea
        self.name = name
        self.end_ea = idc.FindFuncEnd(ea)
        self.size = self.end_ea - ea if self.end_ea != idaapi.BADADDR else 0
        self.pseudocode = ""
        self.assembly = ""
        self.calls = []
        self.called_by = []
        self.class_name = ""
        self.is_constructor = False
        self.is_destructor = False
        
    def analyze(self):
        """Perform detailed analysis of the function"""
        # Get pseudocode if Hex-Rays is available
        try:
            cfunc = ida_hexrays.decompile(self.ea)
            if cfunc:
                self.pseudocode = str(cfunc)
        except:
            self.pseudocode = "// Decompilation failed"
        
        # Get assembly code
        self.assembly = self._get_assembly()
        
        # Analyze cross-references
        self._analyze_xrefs()
        
        # Detect class information
        self._detect_class_info()
    
    def _get_assembly(self):
        """Extract assembly code for the function"""
        asm_lines = []
        ea = self.ea
        while ea < self.end_ea and ea != idaapi.BADADDR:
            disasm = idc.GetDisasm(ea)
            asm_lines.append("0x{:08X}: {}".format(ea, disasm))
            ea = idc.NextHead(ea)
        return "\n".join(asm_lines)
    
    def _analyze_xrefs(self):
        """Analyze cross-references to and from this function"""
        # References TO this function
        for xref in idautils.XrefsTo(self.ea):
            caller_name = idc.GetFunctionName(xref.frm)
            if caller_name:
                self.called_by.append(caller_name)
        
        # References FROM this function
        for xref in idautils.XrefsFrom(self.ea):
            callee_name = idc.GetFunctionName(xref.to)
            if callee_name:
                self.calls.append(callee_name)
    
    def _detect_class_info(self):
        """Detect if this is a class method"""
        if "::" in self.name:
            parts = self.name.split("::")
            if len(parts) >= 2:
                self.class_name = parts[0]
                method_name = parts[1]
                
                # Check for constructor/destructor
                if method_name == self.class_name:
                    self.is_constructor = True
                elif method_name.startswith("~"):
                    self.is_destructor = True

class ZoneServerIDADecompiler:
    """IDA Pro decompiler for ZoneServer binary analysis"""
    
    def __init__(self, output_dir="zoneserver_analysis"):
        self.output_dir = output_dir
        self.functions = {}
        self.classes = {}
        self.game_systems = {}
        self.statistics = {}
        
        # Game system patterns for classification
        self.system_patterns = {
            'Authentication': ['CSAuth', 'Auth', 'Login', 'Verify'],
            'Networking': ['Net', 'Socket', 'Packet', 'Send', 'Recv'],
            'Player Management': ['Player', 'User', 'Character', 'Party'],
            'Game Objects': ['GameObject', 'Monster', 'NPC', 'Item'],
            'Map System': ['Map', 'Zone', 'Level', 'World'],
            'Guild System': ['Guild', 'Clan'],
            'Database': ['DB', 'SQL', 'Query', 'Record'],
            'Security': ['Crypt', 'Hash', 'Encrypt', 'Security'],
            'UI System': ['UI', 'Dialog', 'Window', 'View', 'Frame'],
            'Combat System': ['Attack', 'Damage', 'Skill', 'Battle']
        }
        
        self._setup_output_directory()
        
    def _setup_output_directory(self):
        """Create output directory structure"""
        if not os.path.exists(self.output_dir):
            os.makedirs(self.output_dir)
        
        subdirs = ["functions", "classes", "analysis", "reports"]
        for subdir in subdirs:
            path = os.path.join(self.output_dir, subdir)
            if not os.path.exists(path):
                os.makedirs(path)
        
    def log(self, message):
        """Simple logging function for IDA Pro"""
        print("[ZoneServer Decompiler] {}".format(message))
        
    def analyze_all_functions(self):
        """Analyze all functions in the binary"""
        self.log("Starting function analysis...")
        
        function_count = 0
        for func_ea in idautils.Functions():
            func_name = idc.GetFunctionName(func_ea)
            if not func_name:
                continue
                
            if function_count % 100 == 0:
                self.log("Analyzing function: {}".format(func_name))
            
            # Create function object and analyze
            ida_func = IDAFunction(func_ea, func_name)
            ida_func.analyze()
            
            self.functions[func_name] = ida_func
            function_count += 1
            
            # Progress indicator
            if function_count % 500 == 0:
                self.log("Processed {} functions...".format(function_count))
        
        self.log("Completed analysis of {} functions".format(function_count))
        
    def classify_game_systems(self):
        """Classify functions into game systems"""
        self.log("Classifying game systems...")
        
        for system_name, keywords in self.system_patterns.items():
            self.game_systems[system_name] = []
            
            for func_name, func in self.functions.items():
                for keyword in keywords:
                    if keyword.lower() in func_name.lower():
                        self.game_systems[system_name].append(func_name)
                        break
        
        # Remove empty systems
        self.game_systems = {k: v for k, v in self.game_systems.items() if v}
        
    def identify_classes(self):
        """Identify and group class methods"""
        self.log("Identifying classes...")
        
        for func_name, func in self.functions.items():
            if func.class_name:
                if func.class_name not in self.classes:
                    self.classes[func.class_name] = {
                        'methods': [],
                        'constructors': [],
                        'destructors': [],
                        'vtable_methods': []
                    }
                
                self.classes[func.class_name]['methods'].append(func_name)
                
                if func.is_constructor:
                    self.classes[func.class_name]['constructors'].append(func_name)
                elif func.is_destructor:
                    self.classes[func.class_name]['destructors'].append(func_name)
                
                # Check for vtable methods
                if 'vtbl' in func_name.lower() or 'vftable' in func_name.lower():
                    self.classes[func.class_name]['vtable_methods'].append(func_name)
        
        self.log("Identified {} classes".format(len(self.classes)))
        
    def generate_statistics(self):
        """Generate analysis statistics"""
        self.statistics = {
            'total_functions': len(self.functions),
            'total_classes': len(self.classes),
            'game_systems': len(self.game_systems),
            'largest_functions': [],
            'most_called_functions': [],
            'class_method_counts': {}
        }
        
        # Find largest functions
        sorted_by_size = sorted(self.functions.items(), 
                               key=lambda x: x[1].size, reverse=True)
        self.statistics['largest_functions'] = [
            (name, func.size) for name, func in sorted_by_size[:10]
        ]
        
        # Find most called functions
        sorted_by_calls = sorted(self.functions.items(), 
                                key=lambda x: len(x[1].called_by), reverse=True)
        self.statistics['most_called_functions'] = [
            (name, len(func.called_by)) for name, func in sorted_by_calls[:10]
        ]
        
        # Class method counts
        for class_name, class_data in self.classes.items():
            self.statistics['class_method_counts'][class_name] = len(class_data['methods'])
            
    def export_summary_report(self):
        """Export main summary report"""
        self.log("Exporting summary report...")
        
        summary_file = os.path.join(self.output_dir, "README.txt")
        
        try:
            with open(summary_file, 'w') as f:
                f.write("ZoneServer IDA Pro Analysis Results\n")
                f.write("=" * 40 + "\n\n")
                f.write("This directory contains the complete analysis of the ZoneServer binary.\n\n")
                
                f.write("Analysis Summary:\n")
                f.write("- Binary: {}\n".format(idc.GetInputFile()))
                f.write("- Analysis Date: {}\n".format(time.strftime('%Y-%m-%d %H:%M:%S')))
                f.write("- Total Functions: {}\n".format(self.statistics['total_functions']))
                f.write("- Total Classes: {}\n".format(self.statistics['total_classes']))
                f.write("- Game Systems Identified: {}\n\n".format(self.statistics['game_systems']))
                
                f.write("Directory Structure:\n")
                f.write("zoneserver_analysis/\n")
                f.write("├── functions/          # Individual function analysis\n")
                f.write("├── classes/            # Class analysis and methods\n")
                f.write("├── analysis/           # Game systems categorization\n")
                f.write("├── reports/            # Statistical reports\n")
                f.write("└── README.txt          # This file\n\n")
                
                f.write("Key Findings:\n\n")
                
                # Top game systems
                f.write("Major Game Systems:\n")
                sorted_systems = sorted(self.game_systems.items(), 
                                      key=lambda x: len(x[1]), reverse=True)
                for system_name, functions in sorted_systems[:5]:
                    f.write("- {}: {} functions\n".format(system_name, len(functions)))
                f.write("\n")
                
                # Top classes
                f.write("Largest Classes:\n")
                sorted_classes = sorted(self.statistics['class_method_counts'].items(), 
                                      key=lambda x: x[1], reverse=True)
                for class_name, method_count in sorted_classes[:5]:
                    f.write("- {}: {} methods\n".format(class_name, method_count))
                f.write("\n")
                
        except Exception as e:
            self.log("Error writing summary report: {}".format(e))

    def export_important_functions(self):
        """Export important functions with pseudocode"""
        self.log("Exporting important functions...")

        important_file = os.path.join(self.output_dir, "important_functions.txt")

        # Find important functions
        important_keywords = [
            'Login', 'Auth', 'Player', 'Character', 'Guild', 'Item',
            'Monster', 'Attack', 'Damage', 'Packet', 'Network', 'Socket',
            'Encrypt', 'Decrypt', 'Hash', 'Security', 'Database', 'SQL'
        ]

        important_functions = []
        for func_name in self.functions.keys():
            for keyword in important_keywords:
                if keyword.lower() in func_name.lower():
                    important_functions.append(func_name)
                    break

        try:
            with open(important_file, 'w') as f:
                f.write("ZoneServer Important Functions\n")
                f.write("Generated: {}\n".format(time.strftime('%Y-%m-%d %H:%M:%S')))
                f.write("Total Important Functions: {}\n\n".format(len(important_functions)))

                for func_name in sorted(important_functions)[:50]:  # Limit to first 50
                    func = self.functions[func_name]
                    f.write("\n" + "="*60 + "\n")
                    f.write("Function: {}\n".format(func.name))
                    f.write("Address: 0x{:08X}\n".format(func.ea))
                    f.write("Size: {} bytes\n".format(func.size))
                    f.write("="*60 + "\n")

                    if func.class_name:
                        f.write("Class: {}\n".format(func.class_name))
                        if func.is_constructor:
                            f.write("Type: Constructor\n")
                        elif func.is_destructor:
                            f.write("Type: Destructor\n")
                        f.write("\n")

                    # Add pseudocode if available
                    if func.pseudocode and func.pseudocode != "// Decompilation failed":
                        f.write("Pseudocode:\n")
                        f.write("-" * 20 + "\n")
                        f.write(func.pseudocode)
                        f.write("\n")

                    # Add cross-references
                    if func.called_by:
                        f.write("Called by ({} functions):\n".format(len(func.called_by)))
                        for caller in sorted(set(func.called_by))[:10]:  # Limit to 10
                            f.write("  - {}\n".format(caller))
                        if len(set(func.called_by)) > 10:
                            f.write("  - ... and {} more\n".format(len(set(func.called_by)) - 10))
                        f.write("\n")

                    if func.calls:
                        f.write("Calls ({} functions):\n".format(len(func.calls)))
                        for callee in sorted(set(func.calls))[:10]:  # Limit to 10
                            f.write("  - {}\n".format(callee))
                        if len(set(func.calls)) > 10:
                            f.write("  - ... and {} more\n".format(len(set(func.calls)) - 10))
                        f.write("\n")

        except Exception as e:
            self.log("Error writing important functions: {}".format(e))

    def export_classes_report(self):
        """Export classes analysis"""
        self.log("Exporting classes report...")

        classes_file = os.path.join(self.output_dir, "classes.txt")

        try:
            with open(classes_file, 'w') as f:
                f.write("ZoneServer Classes Analysis\n")
                f.write("Generated: {}\n".format(time.strftime('%Y-%m-%d %H:%M:%S')))
                f.write("Total Classes: {}\n\n".format(len(self.classes)))

                for class_name, class_data in sorted(self.classes.items()):
                    f.write("\nClass: {} ({} methods)\n".format(class_name, len(class_data['methods'])))
                    f.write("-" * 40 + "\n")

                    if class_data['constructors']:
                        f.write("Constructors:\n")
                        for ctor in class_data['constructors']:
                            f.write("  {}\n".format(ctor))
                        f.write("\n")

                    if class_data['destructors']:
                        f.write("Destructors:\n")
                        for dtor in class_data['destructors']:
                            f.write("  {}\n".format(dtor))
                        f.write("\n")

                    if class_data['vtable_methods']:
                        f.write("Virtual Table Methods:\n")
                        for vtable_method in class_data['vtable_methods']:
                            f.write("  {}\n".format(vtable_method))
                        f.write("\n")

                    f.write("All Methods:\n")
                    for method in sorted(class_data['methods'])[:20]:  # Limit to first 20
                        f.write("  {}\n".format(method))
                    if len(class_data['methods']) > 20:
                        f.write("  ... and {} more methods\n".format(len(class_data['methods']) - 20))
                    f.write("\n")

        except Exception as e:
            self.log("Error writing classes report: {}".format(e))

    def export_game_systems_report(self):
        """Export game systems analysis"""
        self.log("Exporting game systems report...")

        systems_file = os.path.join(self.output_dir, "game_systems.txt")

        try:
            with open(systems_file, 'w') as f:
                f.write("ZoneServer Game Systems Analysis\n")
                f.write("Generated: {}\n".format(time.strftime('%Y-%m-%d %H:%M:%S')))
                f.write("This report categorizes functions by game system.\n\n")

                for system_name, functions in self.game_systems.items():
                    if functions:
                        f.write("{}\n".format(system_name))
                        f.write("=" * len(system_name) + "\n")
                        f.write("Function Count: {}\n\n".format(len(functions)))

                        f.write("Functions:\n")
                        for func_name in sorted(functions)[:30]:  # Limit to first 30
                            f.write("- {}\n".format(func_name))

                        if len(functions) > 30:
                            f.write("- ... and {} more functions\n".format(len(functions) - 30))
                        f.write("\n")

        except Exception as e:
            self.log("Error writing game systems report: {}".format(e))

    def run_full_analysis(self):
        """Run complete analysis of the ZoneServer binary"""
        self.log("=" * 60)
        self.log("ZoneServer IDA Pro Decompiler Starting...")
        self.log("=" * 60)

        start_time = time.time()

        try:
            # Step 1: Analyze all functions
            self.analyze_all_functions()

            # Step 2: Classify into game systems
            self.classify_game_systems()

            # Step 3: Identify classes
            self.identify_classes()

            # Step 4: Generate statistics
            self.generate_statistics()

            # Step 5: Export all results
            self.export_summary_report()
            self.export_important_functions()
            self.export_classes_report()
            self.export_game_systems_report()

            end_time = time.time()
            duration = end_time - start_time

            self.log("=" * 60)
            self.log("Analysis Complete!")
            self.log("Duration: {:.2f} seconds".format(duration))
            self.log("Results saved to: {}".format(self.output_dir))
            self.log("=" * 60)

            # Show summary
            print("\n=== ZoneServer Analysis Summary ===")
            print("Functions analyzed: {}".format(self.statistics['total_functions']))
            print("Classes identified: {}".format(self.statistics['total_classes']))
            print("Game systems: {}".format(self.statistics['game_systems']))
            print("Output directory: {}".format(self.output_dir))
            print("Analysis time: {:.2f} seconds".format(duration))

        except Exception as e:
            self.log("Error during analysis: {}".format(e))
            import traceback
            traceback.print_exc()

def main():
    """Main function to run the ZoneServer decompiler"""
    print("ZoneServer IDA Pro Decompiler (Python 2.7)")
    print("=" * 40)

    # Check if we're running in IDA Pro
    try:
        binary_name = idc.GetInputFile()
        print("Analyzing binary: {}".format(binary_name))
    except:
        print("ERROR: This script must be run inside IDA Pro!")
        return

    # Check if Hex-Rays decompiler is available
    try:
        if ida_hexrays.init_hexrays_plugin():
            print("Hex-Rays decompiler detected - pseudocode will be included.")
        else:
            print("WARNING: Hex-Rays decompiler not available. Pseudocode will not be generated.")
    except:
        print("WARNING: Hex-Rays decompiler not available. Pseudocode will not be generated.")

    # Create output directory name based on binary
    binary_base = os.path.splitext(os.path.basename(binary_name))[0]
    output_dir = "{}_analysis".format(binary_base)

    # Create and run decompiler
    decompiler = ZoneServerIDADecompiler(output_dir)
    decompiler.run_full_analysis()

# Run the analysis when script is executed
if __name__ == "__main__":
    main()
else:
    # When loaded as a script in IDA Pro
    main()
